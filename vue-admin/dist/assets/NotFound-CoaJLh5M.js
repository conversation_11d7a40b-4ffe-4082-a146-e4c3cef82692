import{b as a,d as e,e as n,z as d,A as c,B as i,N as l,h as x,f as u}from"./index-ClalJljr.js";const g={class:"not-found min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900"},p={class:"text-center"},_={class:"space-x-4"},y={__name:"NotFound",setup(b){const o=l(),s=()=>{o.go(-1)};return(h,t)=>{const r=i("router-link");return x(),a("div",g,[e("div",p,[t[1]||(t[1]=n('<div class="mb-8"><h1 class="text-9xl font-bold text-gray-300 dark:text-gray-600">404</h1></div><div class="mb-8"><h2 class="text-2xl font-semibold text-gray-900 dark:text-white mb-2">页面未找到</h2><p class="text-gray-600 dark:text-gray-400">抱歉，您访问的页面不存在或已被移除。</p></div>',2)),e("div",_,[e("button",{onClick:s,class:"px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200"}," 返回上页 "),d(r,{to:"/dashboard",class:"inline-block px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200"},{default:c(()=>t[0]||(t[0]=[u(" 回到首页 ")])),_:1,__:[0]})])])])}}};export{y as default};
