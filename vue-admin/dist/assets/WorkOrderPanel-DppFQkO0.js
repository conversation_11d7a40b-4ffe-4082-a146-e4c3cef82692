import{_ as I,r as m,c as W,w as A,a as H,b as E,d as l,z as a,A as n,B as s,h as F,f as _,I as S,J as G,K,t as d,L as f}from"./index-ClalJljr.js";const L={class:"page-container"},R={class:"search-section"},j={class:"layui-card main-card"},Q={class:"layui-card-body"},X={class:"list_search"},Y={class:"table-section"},Z={class:"layui-card main-card table-card"},ee={class:"layui-card-body"},te={class:"flex items-center mb-2"},ae=["src"],le=["title"],ne={class:"mb-1"},oe={class:"ml-1 text-sm"},se=["title"],ie={class:"text-sm font-medium"},de={class:"text-sm text-gray-500"},re={class:"text-sm font-medium"},ce={class:"text-sm text-gray-500"},_e={class:"action-buttons"},ue={class:"pagination-wrapper"},ge={__name:"WorkOrderPanel",setup(pe){const i=m({storeName:"",productName:"",eventType:""}),u=m([]),r=m(1),c=m(10),g=m(0),z=W(()=>{const o=window.innerHeight-300;return Math.max(400,Math.min(600,o))}),C=()=>{console.log("搜索:",i.value),window.g_click&&window.g_click({request:JSON.stringify({action:"工单面板搜索按钮",text:JSON.stringify(i.value)})}),f.success("搜索功能待实现")},V=()=>{i.value={storeName:"",productName:"",eventType:""},window.g_click&&window.g_click({request:JSON.stringify({action:"工单面板重置按钮"})})},O=(o,e)=>{console.log("忽略工单:",o,e),window.g_click&&window.g_click({request:JSON.stringify({action:"工单面板忽略",key:e.orderNumber,data:e})}),f.success("已忽略该工单")},$=(o,e)=>{console.log("复制工单信息:",o,e),window.g_click&&window.g_click({request:JSON.stringify({action:"工单面板复制",key:e.orderNumber,data:e})});const w=`事件类型: ${e.eventType}
商品名称: ${e.productName}
客户昵称: ${e.customerName}
订单号: ${e.orderNumber}`;navigator.clipboard.writeText(w).then(()=>{f.success("工单信息已复制到剪贴板")}).catch(()=>{f.error("复制失败")})},T=o=>{c.value=o,console.log(`每页 ${o} 条`),window.g_click&&window.g_click({request:JSON.stringify({action:"工单面板分页大小改变",pageSize:o})})},J=o=>{r.value=o,console.log(`当前页: ${o}`),window.g_click&&window.g_click({request:JSON.stringify({action:"工单面板分页按钮",page:o})})},y=()=>{window.table_gongdan=u.value,window.table_gongdan_currentPage=r.value,window.table_gongdan_pageSize=c.value,window.table_gongdan_total=g.value};A([u,r,c,g],()=>{y()},{deep:!0});const x=()=>{window.table_gongdan&&Array.isArray(window.table_gongdan)&&(u.value=window.table_gongdan),window.table_gongdan_currentPage&&(r.value=window.table_gongdan_currentPage),window.table_gongdan_pageSize&&(c.value=window.table_gongdan_pageSize),window.table_gongdan_total&&(g.value=window.table_gongdan_total)},P=()=>{u.value=[],g.value=0,r.value=1};return window.updateWorkOrderData=x,H(()=>{window.table_gongdan&&window.table_gongdan.length>0?x():(P(),y())}),(o,e)=>{const w=s("el-input"),b=s("el-col"),h=s("el-option"),q=s("el-select"),N=s("el-icon"),v=s("el-button"),D=s("el-row"),k=s("el-tag"),p=s("el-table-column"),M=s("el-table"),U=s("el-pagination");return F(),E("div",L,[l("div",R,[l("div",j,[e[7]||(e[7]=l("div",{class:"layui-card-header"},"工单面板",-1)),l("div",Q,[l("div",X,[a(D,{gutter:20},{default:n(()=>[a(b,{span:6},{default:n(()=>[a(w,{modelValue:i.value.storeName,"onUpdate:modelValue":e[0]||(e[0]=t=>i.value.storeName=t),placeholder:"店铺名称",clearable:""},null,8,["modelValue"])]),_:1}),a(b,{span:6},{default:n(()=>[a(w,{modelValue:i.value.productName,"onUpdate:modelValue":e[1]||(e[1]=t=>i.value.productName=t),placeholder:"商品名称",clearable:""},null,8,["modelValue"])]),_:1}),a(b,{span:6},{default:n(()=>[a(q,{modelValue:i.value.eventType,"onUpdate:modelValue":e[2]||(e[2]=t=>i.value.eventType=t),placeholder:"事件类型",clearable:"",style:{width:"100%"}},{default:n(()=>[a(h,{label:"商品不满意",value:"unsatisfied"}),a(h,{label:"超时未发货",value:"timeout"}),a(h,{label:"运费补偿",value:"shipping"})]),_:1},8,["modelValue"])]),_:1}),a(b,{span:6},{default:n(()=>[a(v,{type:"primary",onClick:C,class:"btn2"},{default:n(()=>[a(N,null,{default:n(()=>[a(S(G))]),_:1}),e[5]||(e[5]=_(" 搜索 "))]),_:1,__:[5]}),a(v,{onClick:V,class:"btn6"},{default:n(()=>[a(N,null,{default:n(()=>[a(S(K))]),_:1}),e[6]||(e[6]=_(" 重置 "))]),_:1,__:[6]})]),_:1})]),_:1})])])])]),l("div",Y,[l("div",Z,[l("div",ee,[a(M,{data:u.value,style:{width:"100%"},stripe:"",border:"","max-height":z.value,class:"stable-table"},{default:n(()=>[a(p,{label:"事件类型","min-width":"300"},{default:n(t=>[l("div",te,[l("img",{src:t.row.platformIcon,alt:"平台",class:"w-6 h-6 mr-2"},null,8,ae),a(k,{color:t.row.eventColor,size:"small",style:{color:"white",border:"none"}},{default:n(()=>[_(d(t.row.eventType),1)]),_:2},1032,["color"])]),l("p",{class:"text-xs text-gray-500 line-clamp-2",title:t.row.description},d(t.row.description),9,le)]),_:1}),a(p,{label:"商品名称","min-width":"300"},{default:n(t=>[l("div",ne,[a(k,{type:"primary",size:"small"},{default:n(()=>e[8]||(e[8]=[_("店铺")])),_:1,__:[8]}),l("span",oe,d(t.row.storeName),1)]),l("p",{class:"text-sm text-gray-900 line-clamp-2",title:t.row.productName},d(t.row.productName),9,se)]),_:1}),a(p,{label:"对方昵称/订单号","min-width":"200"},{default:n(t=>[l("p",ie,d(t.row.customerName),1),l("p",de,d(t.row.orderNumber),1)]),_:1}),a(p,{label:"快递单号/物流状态","min-width":"200"},{default:n(t=>[l("p",re,d(t.row.trackingNumber),1),l("p",ce,d(t.row.logisticsStatus),1)]),_:1}),a(p,{label:"操作",width:"120",fixed:"right"},{default:n(t=>[l("div",_e,[a(v,{size:"small",type:"primary",onClick:B=>O(t.$index,t.row),class:"btn2 action-btn"},{default:n(()=>e[9]||(e[9]=[_(" 忽略 ")])),_:2,__:[9]},1032,["onClick"]),a(v,{size:"small",type:"danger",onClick:B=>$(t.$index,t.row),class:"btn4 action-btn"},{default:n(()=>e[10]||(e[10]=[_(" 复制 ")])),_:2,__:[10]},1032,["onClick"])])]),_:1})]),_:1},8,["data","max-height"])]),l("div",ue,[a(U,{"current-page":r.value,"onUpdate:currentPage":e[3]||(e[3]=t=>r.value=t),"page-size":c.value,"onUpdate:pageSize":e[4]||(e[4]=t=>c.value=t),"page-sizes":[10,20,50,100],total:g.value,layout:"prev, pager, next","prev-text":"上一页","next-text":"下一页",onSizeChange:T,onCurrentChange:J,class:"layui-pagination"},null,8,["current-page","page-size","total"])])])])])}}},we=I(ge,[["__scopeId","data-v-a08bd182"]]);export{we as default};
