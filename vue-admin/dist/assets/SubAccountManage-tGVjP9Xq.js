import{_ as q,r as b,i as j,c as D,p as K,j as W,k as X,l as Y,m as Q,q as Z,s as ee,v as te,a as O,o as ne,x as P,b as S,y as V,z as e,A as l,F as L,g as ae,B as d,C as le,d as w,D as oe,t as $,E as ie,G as se,T as ce,n as ue,h as v,H as de,w as re,f as N,I as U,J as _e,K as pe,L as me}from"./index-ClalJljr.js";const we={key:0,class:"context-menu-divider"},fe={key:1,class:"context-menu-divider"},he={__name:"ContextMenu",props:{customMenuItems:{type:Array,default:()=>[]}},emits:["menu-click"],setup(H,{expose:p,emit:k}){const f=H,y=k,r=b(!1),h=b(null),M=b(null),o=j({x:0,y:0});let m=null;const R=[{action:"子账号管理_添加子账号",label:"添加子账号",icon:K,type:"add"},{action:"子账号管理_删除子账号",label:"删除子账号",icon:W,type:"danger"},{action:"子账号管理_充值点数",label:"充值点数",icon:X,type:"normal"},{action:"子账号管理_回收点数",label:"回收点数",icon:Y,type:"normal"},{action:"子账号管理_禁用账户",label:"禁用账户",icon:Q,type:"normal"},{action:"子账号管理_复制账号密码",label:"复制账号密码",icon:Z,type:"normal"},{action:"子账号管理_修改登陆密码",label:"修改登陆密码",icon:ee,type:"normal"},{action:"子账号管理_设置账号等级",label:"设置账号等级",icon:te,type:"normal"}],A=D(()=>f.customMenuItems.length>0?f.customMenuItems:R),B=D(()=>({position:"fixed",left:`${o.x}px`,top:`${o.y}px`,zIndex:9999})),E=(a,t,g)=>{m&&clearTimeout(m),r.value&&x(),m=setTimeout(()=>{M.value=g,o.x=a,o.y=t,r.value=!0,ue(()=>{T()})},10)},x=()=>{m&&(clearTimeout(m),m=null),r.value=!1,M.value=null},T=()=>{if(!h.value)return;const t=h.value.getBoundingClientRect(),g=window.innerWidth,C=window.innerHeight,s=10;if(o.x+t.width>g-s){const u=o.x-t.width;u>=s?o.x=u:o.x=g-t.width-s}if(o.y+t.height>C-s){const u=o.y-t.height;u>=s?o.y=u:o.y=C-t.height-s}o.x<s&&(o.x=s),o.y<s&&(o.y=s)},I=async a=>{const t=M.value;if(x(),a==="子账号管理_删除子账号")try{await de.confirm(`确定要删除账号 ${t?.account} 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!1}),i(a,t)}catch{}else i(a,t)},i=(a,t)=>{window.g_click&&window.g_click({request:JSON.stringify({action:a,account:t?.account,data:t})}),y("menu-click",{action:a,data:t})},n=a=>{r.value&&h.value&&!h.value.contains(a.target)&&x()},z=a=>{a.key==="Escape"&&r.value&&x()};return O(()=>{document.addEventListener("click",n),document.addEventListener("keydown",z)}),ne(()=>{document.removeEventListener("click",n),document.removeEventListener("keydown",z),m&&(clearTimeout(m),m=null)}),p({show:E,hide:x}),(a,t)=>{const g=d("el-icon"),C=d("el-menu-item"),s=d("el-menu");return v(),P(ce,{to:"body"},[r.value?(v(),S("div",{key:0,ref_key:"menuRef",ref:h,class:"context-menu",style:se(B.value),onClick:t[0]||(t[0]=ie(()=>{},["stop"]))},[e(s,{class:"context-menu-list",onSelect:I},{default:l(()=>[(v(!0),S(L,null,ae(A.value,(u,_)=>(v(),S(L,{key:u.action},[_===1?(v(),S("div",we)):V("",!0),e(C,{index:u.action,class:le(u.type)},{default:l(()=>[u.icon?(v(),P(g,{key:0},{default:l(()=>[(v(),P(oe(u.icon)))]),_:2},1024)):V("",!0),w("span",null,$(u.label),1)]),_:2},1032,["index","class"]),_===3?(v(),S("div",fe)):V("",!0)],64))),128))]),_:1})],4)):V("",!0)])}}},ge=q(he,[["__scopeId","data-v-c24b576c"]]),ve={class:"page-container"},be={class:"search-section"},ye={class:"layui-card main-card"},xe={class:"layui-card-body"},ze={class:"list_search"},ke={class:"table-section"},Ce={class:"layui-card main-card table-card"},Se={class:"layui-card-body"},Me={class:"table-wrapper p-4"},Te={class:"pagination-wrapper"},Ve={__name:"SubAccountManage",setup(H){const p=b({account:"",status:"",online:""}),k=b([]),f=b(1),y=b(10),r=b(0),h=b(null),M=D(()=>{const i=window.innerHeight-300;return Math.max(400,Math.min(600,i))}),o=()=>{console.log("搜索:",p.value),window.g_click&&window.g_click({request:JSON.stringify({action:"子账号管理搜索按钮",text:JSON.stringify(p.value)})}),me.success("搜索功能待实现")},m=()=>{p.value={account:"",status:"",online:""},window.g_click&&window.g_click({request:JSON.stringify({action:"子账号管理重置按钮"})})},R=i=>{y.value=i,console.log(`每页 ${i} 条`),window.g_click&&window.g_click({request:JSON.stringify({action:"子账号管理分页大小改变",pageSize:i})})},A=i=>{f.value=i,console.log(`当前页: ${i}`),window.g_click&&window.g_click({request:JSON.stringify({action:"子账号管理分页按钮",page:i})})},B=(i,n,z)=>{z.preventDefault(),h.value&&h.value.show(z.clientX,z.clientY,i)},E=({action:i,data:n})=>{},x=()=>{window.table_zizhanghao=k.value,window.table_zizhanghao_currentPage=f.value,window.table_zizhanghao_pageSize=y.value,window.table_zizhanghao_total=r.value};re([k,f,y,r],()=>{x()},{deep:!0});const T=()=>{window.table_zizhanghao&&Array.isArray(window.table_zizhanghao)&&(k.value=window.table_zizhanghao),window.table_zizhanghao_currentPage&&(f.value=window.table_zizhanghao_currentPage),window.table_zizhanghao_pageSize&&(y.value=window.table_zizhanghao_pageSize),window.table_zizhanghao_total&&(r.value=window.table_zizhanghao_total)},I=()=>{k.value=[],r.value=0,f.value=1};return window.updateSubAccountData=T,O(()=>{window.table_zizhanghao&&window.table_zizhanghao.length>0?T():(I(),x())}),(i,n)=>{const z=d("el-input"),a=d("el-col"),t=d("el-option"),g=d("el-select"),C=d("el-icon"),s=d("el-button"),u=d("el-row"),_=d("el-table-column"),J=d("el-tag"),F=d("el-table"),G=d("el-pagination");return v(),S("div",ve,[w("div",be,[w("div",ye,[n[7]||(n[7]=w("div",{class:"layui-card-header"},"子账号管理",-1)),w("div",xe,[w("div",ze,[e(u,{gutter:20},{default:l(()=>[e(a,{span:6},{default:l(()=>[e(z,{modelValue:p.value.account,"onUpdate:modelValue":n[0]||(n[0]=c=>p.value.account=c),placeholder:"搜索账号",clearable:""},null,8,["modelValue"])]),_:1}),e(a,{span:6},{default:l(()=>[e(g,{modelValue:p.value.status,"onUpdate:modelValue":n[1]||(n[1]=c=>p.value.status=c),placeholder:"账号状态",clearable:"",style:{width:"100%"}},{default:l(()=>[e(t,{label:"正常",value:"normal"}),e(t,{label:"禁用",value:"disabled"})]),_:1},8,["modelValue"])]),_:1}),e(a,{span:6},{default:l(()=>[e(g,{modelValue:p.value.online,"onUpdate:modelValue":n[2]||(n[2]=c=>p.value.online=c),placeholder:"是否在线",clearable:"",style:{width:"100%"}},{default:l(()=>[e(t,{label:"在线",value:"online"}),e(t,{label:"离线",value:"offline"})]),_:1},8,["modelValue"])]),_:1}),e(a,{span:6},{default:l(()=>[e(s,{type:"primary",onClick:o,class:"btn2"},{default:l(()=>[e(C,null,{default:l(()=>[e(U(_e))]),_:1}),n[5]||(n[5]=N(" 搜索 "))]),_:1,__:[5]}),e(s,{onClick:m,class:"btn6"},{default:l(()=>[e(C,null,{default:l(()=>[e(U(pe))]),_:1}),n[6]||(n[6]=N(" 重置 "))]),_:1,__:[6]})]),_:1})]),_:1})])])])]),w("div",ke,[w("div",Ce,[w("div",Se,[w("div",Me,[e(F,{data:k.value,style:{width:"100%"},stripe:"",border:"","max-height":M.value,class:"stable-table",onRowContextmenu:B},{default:l(()=>[e(_,{prop:"account",label:"账号","min-width":"80"}),e(_,{prop:"level",label:"等级","min-width":"60"}),e(_,{prop:"balance",label:"余额","min-width":"70"}),e(_,{prop:"expireTime",label:"到期时间","min-width":"100"}),e(_,{prop:"status",label:"状态","min-width":"60"},{default:l(c=>[e(J,{type:c.row.status==="正常"?"success":"danger",size:"small"},{default:l(()=>[N($(c.row.status),1)]),_:2},1032,["type"])]),_:1}),e(_,{prop:"loginTime",label:"登陆时间","min-width":"100"}),e(_,{prop:"online",label:"在线","min-width":"50"},{default:l(c=>[e(J,{type:c.row.online==="在线"?"success":"info",size:"small"},{default:l(()=>[N($(c.row.online),1)]),_:2},1032,["type"])]),_:1}),e(_,{prop:"storeCount",label:"店铺","min-width":"50"}),e(_,{prop:"onlineStoreCount",label:"在线店铺","min-width":"70"})]),_:1},8,["data","max-height"])]),w("div",Te,[e(G,{"current-page":f.value,"onUpdate:currentPage":n[3]||(n[3]=c=>f.value=c),"page-size":y.value,"onUpdate:pageSize":n[4]||(n[4]=c=>y.value=c),"page-sizes":[10,20,50,100],total:r.value,layout:"prev, pager, next","prev-text":"上一页","next-text":"下一页",onSizeChange:R,onCurrentChange:A,class:"layui-pagination"},null,8,["current-page","page-size","total"])])])])]),e(ge,{ref_key:"contextMenuRef",ref:h,onMenuClick:E},null,512)])}}},Re=q(Ve,[["__scopeId","data-v-3b47aef6"]]);export{Re as default};
