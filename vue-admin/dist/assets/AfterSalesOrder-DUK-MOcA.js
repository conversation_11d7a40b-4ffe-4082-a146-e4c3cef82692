import{_ as U,r as h,c as B,w as H,a as E,b as F,d as l,z as e,A as o,B as n,h as G,f as c,I as S,J as K,K as L,t as i,L as k}from"./index-ClalJljr.js";const R={class:"page-container"},j={class:"search-section"},Q={class:"layui-card main-card"},W={class:"layui-card-body"},X={class:"list_search"},Y={class:"table-section"},Z={class:"layui-card main-card table-card"},ee={class:"layui-card-body"},te={class:"table-wrapper"},ae=["src"],le=["title"],oe={class:"text-xs text-gray-500 mt-1"},se={class:"ml-2"},ne={class:"text-sm"},ie={class:"font-semibold"},de={class:"font-semibold text-red-600"},ue={class:"text-xs text-gray-500 mt-1"},re={class:"text-sm font-medium"},ce={class:"text-sm text-gray-500"},_e={class:"action-buttons"},pe={class:"pagination-wrapper"},we={__name:"AfterSalesOrder",setup(me){const d=h({productName:"",afterSalesType:"",status:""}),w=h([]),r=h(1),_=h(10),m=h(0),N=B(()=>{const s=window.innerHeight-300;return Math.max(400,Math.min(600,s))}),z=()=>{console.log("搜索:",d.value),window.g_click&&window.g_click({request:JSON.stringify({action:"售后工单搜索按钮",text:JSON.stringify(d.value)})}),k.success("搜索功能待实现")},C=()=>{d.value={productName:"",afterSalesType:"",status:""},window.g_click&&window.g_click({request:JSON.stringify({action:"售后工单重置按钮"})})},V=(s,t)=>{console.log("忽略售后工单:",s,t),window.g_click&&window.g_click({request:JSON.stringify({action:"售后工单忽略",key:t.orderNumber,data:t})}),k.success("已忽略该售后工单")},I=(s,t)=>{console.log("查看售后工单详情:",s,t),window.g_click&&window.g_click({request:JSON.stringify({action:"售后工单查看详情",key:t.orderNumber,data:t})})},O=s=>{_.value=s,console.log(`每页 ${s} 条`),window.g_click&&window.g_click({request:JSON.stringify({action:"售后工单分页大小改变",pageSize:s})})},J=s=>{r.value=s,console.log(`当前页: ${s}`),window.g_click&&window.g_click({request:JSON.stringify({action:"售后工单分页按钮",page:s})})},b=()=>{window.table_shouhou=w.value,window.table_shouhou_currentPage=r.value,window.table_shouhou_pageSize=_.value,window.table_shouhou_total=m.value};H([w,r,_,m],()=>{b()},{deep:!0});const v=()=>{window.table_shouhou&&Array.isArray(window.table_shouhou)&&(w.value=window.table_shouhou),window.table_shouhou_currentPage&&(r.value=window.table_shouhou_currentPage),window.table_shouhou_pageSize&&(_.value=window.table_shouhou_pageSize),window.table_shouhou_total&&(m.value=window.table_shouhou_total)},q=()=>{w.value=[],m.value=0,r.value=1};return window.updateAfterSalesData=v,E(()=>{window.table_shouhou&&window.table_shouhou.length>0?v():(q(),b())}),(s,t)=>{const A=n("el-input"),g=n("el-col"),p=n("el-option"),y=n("el-select"),x=n("el-icon"),f=n("el-button"),D=n("el-row"),u=n("el-table-column"),T=n("el-tag"),$=n("el-table"),M=n("el-pagination");return G(),F("div",R,[l("div",j,[l("div",Q,[t[7]||(t[7]=l("div",{class:"layui-card-header"},"售后工单",-1)),l("div",W,[l("div",X,[e(D,{gutter:20},{default:o(()=>[e(g,{span:6},{default:o(()=>[e(A,{modelValue:d.value.productName,"onUpdate:modelValue":t[0]||(t[0]=a=>d.value.productName=a),placeholder:"商品名称",clearable:""},null,8,["modelValue"])]),_:1}),e(g,{span:6},{default:o(()=>[e(y,{modelValue:d.value.afterSalesType,"onUpdate:modelValue":t[1]||(t[1]=a=>d.value.afterSalesType=a),placeholder:"售后类型",clearable:"",style:{width:"100%"}},{default:o(()=>[e(p,{label:"商品不满意",value:"unsatisfied"}),e(p,{label:"质量问题",value:"quality"}),e(p,{label:"商品损坏",value:"damage"})]),_:1},8,["modelValue"])]),_:1}),e(g,{span:6},{default:o(()=>[e(y,{modelValue:d.value.status,"onUpdate:modelValue":t[2]||(t[2]=a=>d.value.status=a),placeholder:"售后状态",clearable:"",style:{width:"100%"}},{default:o(()=>[e(p,{label:"待处理",value:"pending"}),e(p,{label:"处理中",value:"processing"}),e(p,{label:"已完成",value:"completed"})]),_:1},8,["modelValue"])]),_:1}),e(g,{span:6},{default:o(()=>[e(f,{type:"primary",onClick:z,class:"btn2"},{default:o(()=>[e(x,null,{default:o(()=>[e(S(K))]),_:1}),t[5]||(t[5]=c(" 搜索 "))]),_:1,__:[5]}),e(f,{onClick:C,class:"btn6"},{default:o(()=>[e(x,null,{default:o(()=>[e(S(L))]),_:1}),t[6]||(t[6]=c(" 重置 "))]),_:1,__:[6]})]),_:1})]),_:1})])])])]),l("div",Y,[l("div",Z,[l("div",ee,[l("div",te,[e($,{data:w.value,style:{width:"100%"},stripe:"",border:"","max-height":N.value,class:"stable-table"},{default:o(()=>[e(u,{width:"60"},{default:o(a=>[l("img",{src:a.row.platformIcon,alt:"平台",class:"w-6 h-6"},null,8,ae)]),_:1}),e(u,{prop:"orderNumber",label:"售后编号","min-width":"150"}),e(u,{label:"商品信息","min-width":"300"},{default:o(a=>[l("p",{class:"text-sm font-medium line-clamp-2",title:a.row.productInfo.name},i(a.row.productInfo.name),9,le),l("div",oe,[l("span",null,"ID："+i(a.row.productInfo.id),1),l("span",se,"规格："+i(a.row.productInfo.spec),1)])]),_:1}),e(u,{label:"金额","min-width":"180"},{default:o(a=>[l("div",ne,[l("div",null,[t[8]||(t[8]=c("付款金额：")),l("span",ie,"¥"+i(a.row.amount.paid),1)]),l("div",null,[t[9]||(t[9]=c("售后金额：")),l("span",de,"¥"+i(a.row.amount.afterSales),1)])])]),_:1}),e(u,{prop:"applyTime",label:"售后申请时间","min-width":"180"}),e(u,{label:"售后类型","min-width":"120"},{default:o(a=>[e(T,{color:a.row.typeColor,size:"small",style:{color:"white",border:"none"}},{default:o(()=>[c(i(a.row.type),1)]),_:2},1032,["color"]),l("p",ue,i(a.row.status),1)]),_:1}),e(u,{prop:"reason",label:"申请原因","min-width":"200","show-overflow-tooltip":""}),e(u,{label:"快递信息","min-width":"200"},{default:o(a=>[l("p",re,i(a.row.logistics.company)+" "+i(a.row.logistics.number),1),l("p",ce,i(a.row.logistics.status),1)]),_:1}),e(u,{label:"操作",width:"120",fixed:"right"},{default:o(a=>[l("div",_e,[e(f,{size:"small",type:"primary",onClick:P=>V(a.$index,a.row),class:"btn2 action-btn"},{default:o(()=>t[10]||(t[10]=[c(" 忽略 ")])),_:2,__:[10]},1032,["onClick"]),e(f,{size:"small",type:"danger",onClick:P=>I(a.$index,a.row),class:"btn4 action-btn"},{default:o(()=>t[11]||(t[11]=[c(" 查看详情 ")])),_:2,__:[11]},1032,["onClick"])])]),_:1})]),_:1},8,["data","max-height"])]),l("div",pe,[e(M,{"current-page":r.value,"onUpdate:currentPage":t[3]||(t[3]=a=>r.value=a),"page-size":_.value,"onUpdate:pageSize":t[4]||(t[4]=a=>_.value=a),"page-sizes":[10,20,50,100],total:m.value,layout:"prev, pager, next","prev-text":"上一页","next-text":"下一页",onSizeChange:O,onCurrentChange:J,class:"layui-pagination"},null,8,["current-page","page-size","total"])])])])])])}}},ge=U(we,[["__scopeId","data-v-790e0bff"]]);export{ge as default};
