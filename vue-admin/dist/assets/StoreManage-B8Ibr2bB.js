import{_ as q,r as h,i as j,c as $,M as D,a as A,o as X,x as H,b as T,y as I,z as e,A as s,F as J,g as Y,B as u,C as Q,d as _,D as Z,t as F,E as ee,G as te,T as ne,n as ae,h as k,H as L,w as oe,f as P,I as O,J as le,K as ie,L as se}from"./index-ClalJljr.js";const ce={key:0,class:"context-menu-divider"},ue={__name:"StoreContextMenu",props:{customMenuItems:{type:Array,default:()=>[]}},emits:["menu-click"],setup(U,{expose:g,emit:S}){const p=U,y=S,r=h(!1),v=h(null),M=h(null),o=j({x:0,y:0});let d=null;const N=[{action:"店铺管理_清空进线次数",label:"清空进线次数",icon:D,type:"normal"},{action:"店铺管理_清空转接次数",label:"清空转接次数",icon:D,type:"normal"}],B=$(()=>p.customMenuItems.length>0?p.customMenuItems:N),R=$(()=>({position:"fixed",left:`${o.x}px`,top:`${o.y}px`,zIndex:9999})),V=(n,t,m)=>{d&&clearTimeout(d),r.value&&b(),d=setTimeout(()=>{M.value=m,o.x=n,o.y=t,r.value=!0,ae(()=>{z()})},10)},b=()=>{d&&(clearTimeout(d),d=null),r.value=!1,M.value=null},z=()=>{if(!v.value)return;const t=v.value.getBoundingClientRect(),m=window.innerWidth,C=window.innerHeight,i=10;if(o.x+t.width>m-i){const c=o.x-t.width;c>=i?o.x=c:o.x=m-t.width-i}if(o.y+t.height>C-i){const c=o.y-t.height;c>=i?o.y=c:o.y=C-t.height-i}o.x<i&&(o.x=i),o.y<i&&(o.y=i)},E=async n=>{const t=M.value;if(b(),n==="店铺管理_删除店铺")try{await L.confirm(`确定要删除店铺 "${t?.name}" 吗？此操作不可恢复！`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error",dangerouslyUseHTMLString:!1}),l(n,t)}catch{}else if(n==="店铺管理_清空进线次数")try{await L.confirm(`确定要清空店铺 "${t?.name}" 的进线次数吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!1}),l(n,t)}catch{}else if(n==="店铺管理_清空转接次数")try{await L.confirm(`确定要清空店铺 "${t?.name}" 的转接次数吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!1}),l(n,t)}catch{}else l(n,t)},l=(n,t)=>{if(window.g_click){const m={店铺管理_清空进线次数:"清空进线次数",店铺管理_清空转接次数:"清空转接次数",店铺管理_编辑店铺:"编辑店铺",店铺管理_删除店铺:"删除店铺"};window.g_click({request:JSON.stringify({action:m[n]||n,storeName:t?.name,account:t?.account,data:t})})}y("menu-click",{action:n,data:t})},a=n=>{r.value&&v.value&&!v.value.contains(n.target)&&b()},x=n=>{n.key==="Escape"&&r.value&&b()};return A(()=>{document.addEventListener("click",a),document.addEventListener("keydown",x)}),X(()=>{document.removeEventListener("click",a),document.removeEventListener("keydown",x),d&&(clearTimeout(d),d=null)}),g({show:V,hide:b}),(n,t)=>{const m=u("el-icon"),C=u("el-menu-item"),i=u("el-menu");return k(),H(ne,{to:"body"},[r.value?(k(),T("div",{key:0,ref_key:"menuRef",ref:v,class:"context-menu",style:te(R.value),onClick:t[0]||(t[0]=ee(()=>{},["stop"]))},[e(i,{class:"context-menu-list",onSelect:E},{default:s(()=>[(k(!0),T(J,null,Y(B.value,(c,w)=>(k(),T(J,{key:c.action},[e(C,{index:c.action,class:Q(c.type)},{default:s(()=>[c.icon?(k(),H(m,{key:0},{default:s(()=>[(k(),H(Z(c.icon)))]),_:2},1024)):I("",!0),_("span",null,F(c.label),1)]),_:2},1032,["index","class"]),w===1?(k(),T("div",ce)):I("",!0)],64))),128))]),_:1})],4)):I("",!0)])}}},re=q(ue,[["__scopeId","data-v-dd27a8af"]]),de={class:"page-container"},_e={class:"search-section"},pe={class:"layui-card main-card"},me={class:"layui-card-body"},we={class:"list_search"},fe={class:"table-section"},ge={class:"layui-card main-card table-card"},ve={class:"layui-card-body"},he={class:"table-wrapper"},ye={class:"pagination-wrapper"},be={__name:"StoreManage",setup(U){const g=h({storeName:"",online:""}),S=h([]),p=h(1),y=h(10),r=h(0),v=h(null),M=$(()=>{const l=window.innerHeight-300;return Math.max(400,Math.min(600,l))}),o=()=>{console.log("搜索:",g.value),window.g_click&&window.g_click({request:JSON.stringify({action:"店铺管理搜索按钮",text:JSON.stringify(g.value)})}),se.success("搜索功能待实现")},d=()=>{g.value={storeName:"",online:""},window.g_click&&window.g_click({request:JSON.stringify({action:"店铺管理重置按钮"})})},N=(l,a,x)=>{x.preventDefault(),v.value&&v.value.show(x.clientX,x.clientY,l)},B=({action:l,data:a})=>{},R=l=>{y.value=l,console.log(`每页 ${l} 条`),window.g_click&&window.g_click({request:JSON.stringify({action:"店铺管理分页大小改变",pageSize:l})})},V=l=>{p.value=l,console.log(`当前页: ${l}`),window.g_click&&window.g_click({request:JSON.stringify({action:"店铺管理分页按钮",page:l})})},b=()=>{window.table_dianpu=S.value,window.table_dianpu_currentPage=p.value,window.table_dianpu_pageSize=y.value,window.table_dianpu_total=r.value};oe([S,p,y,r],()=>{b()},{deep:!0});const z=()=>{window.table_dianpu&&Array.isArray(window.table_dianpu)&&(S.value=window.table_dianpu),window.table_dianpu_currentPage&&(p.value=window.table_dianpu_currentPage),window.table_dianpu_pageSize&&(y.value=window.table_dianpu_pageSize),window.table_dianpu_total&&(r.value=window.table_dianpu_total)},E=()=>{S.value=[],r.value=0,p.value=1};return window.updateStoreData=z,A(()=>{window.table_dianpu&&window.table_dianpu.length>0?z():(E(),b())}),(l,a)=>{const x=u("el-input"),n=u("el-col"),t=u("el-option"),m=u("el-select"),C=u("el-icon"),i=u("el-button"),c=u("el-row"),w=u("el-table-column"),G=u("el-tag"),K=u("el-table"),W=u("el-pagination");return k(),T("div",de,[_("div",_e,[_("div",pe,[a[6]||(a[6]=_("div",{class:"layui-card-header"},"店铺管理",-1)),_("div",me,[_("div",we,[e(c,{gutter:20},{default:s(()=>[e(n,{span:8},{default:s(()=>[e(x,{modelValue:g.value.storeName,"onUpdate:modelValue":a[0]||(a[0]=f=>g.value.storeName=f),placeholder:"店铺名称",clearable:""},null,8,["modelValue"])]),_:1}),e(n,{span:8},{default:s(()=>[e(m,{modelValue:g.value.online,"onUpdate:modelValue":a[1]||(a[1]=f=>g.value.online=f),placeholder:"是否在线",clearable:"",style:{width:"100%"}},{default:s(()=>[e(t,{label:"在线",value:"online"}),e(t,{label:"离线",value:"offline"})]),_:1},8,["modelValue"])]),_:1}),e(n,{span:8},{default:s(()=>[e(i,{type:"primary",onClick:o,class:"btn2"},{default:s(()=>[e(C,null,{default:s(()=>[e(O(le))]),_:1}),a[4]||(a[4]=P(" 搜索 "))]),_:1,__:[4]}),e(i,{onClick:d,class:"btn6"},{default:s(()=>[e(C,null,{default:s(()=>[e(O(ie))]),_:1}),a[5]||(a[5]=P(" 重置 "))]),_:1,__:[5]})]),_:1})]),_:1})])])])]),_("div",fe,[_("div",ge,[_("div",ve,[_("div",he,[e(K,{data:S.value,style:{width:"100%"},stripe:"",border:"","max-height":M.value,class:"stable-table",onRowContextmenu:N},{default:s(()=>[e(w,{prop:"name",label:"店铺名称","min-width":"150"}),e(w,{prop:"todayReception",label:"今日接待","min-width":"100"}),e(w,{prop:"pointsConsumed",label:"消耗点数","min-width":"100"}),e(w,{prop:"incomingCount",label:"进线次数","min-width":"100"}),e(w,{prop:"transferCount",label:"转接次数","min-width":"100"}),e(w,{prop:"account",label:"所属账号","min-width":"120"}),e(w,{prop:"createTime",label:"入库时间","min-width":"180"}),e(w,{prop:"online",label:"是否在线","min-width":"100"},{default:s(f=>[e(G,{type:f.row.online==="在线"?"success":"info",size:"small"},{default:s(()=>[P(F(f.row.online),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data","max-height"])]),_("div",ye,[e(W,{"current-page":p.value,"onUpdate:currentPage":a[2]||(a[2]=f=>p.value=f),"page-size":y.value,"onUpdate:pageSize":a[3]||(a[3]=f=>y.value=f),"page-sizes":[10,20,50,100],total:r.value,layout:"prev, pager, next","prev-text":"上一页","next-text":"下一页",onSizeChange:R,onCurrentChange:V,class:"layui-pagination"},null,8,["current-page","page-size","total"])])])])]),e(re,{ref_key:"contextMenuRef",ref:v,onMenuClick:B},null,512)])}}},ke=q(be,[["__scopeId","data-v-********"]]);export{ke as default};
