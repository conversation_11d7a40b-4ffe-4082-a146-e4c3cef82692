@import "tailwindcss";
@import "./old-styles.css";

/* 确保原始样式优先级 */
.main-content {
  margin-left: 150px !important;
}

.main-content.collapsed {
  margin-left: 80px !important;
}

@media (max-width: 768px) {
  .main-content,
  .main-content.collapsed {
    margin-left: 0 !important;
  }
}

/* 图标字体 */
@font-face {
  font-family: 'iconfont';
  src: url('//at.alicdn.com/t/c/font_4973147_b6g1ritc17.woff2?t=1752327680529') format('woff2'),
       url('//at.alicdn.com/t/c/font_4973147_b6g1ritc17.woff?t=1752327680529') format('woff'),
       url('//at.alicdn.com/t/c/font_4973147_b6g1ritc17.ttf?t=1752327680529') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 自定义样式 */
:root {
  /* 主题颜色变量 */
  --color-primary: #7748f8;
  --color-success: #11cea2;
  --color-warning: #f5840a;
  --color-danger: #e76537;
  --color-info: #3468e5;

  /* 背景颜色 */
  --bg-main: #f5f7fa;
  --bg-card: #ffffff;
  --bg-sidebar: #ffffff;

  /* 文字颜色 */
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-muted: #c2c2c2;

  /* 边框颜色 */
  --border-color: #e5e7eb;
}

/* 暗色主题 */
.dark-theme {
  --bg-main: #1a1a1a;
  --bg-card: #2d2d2d;
  --bg-sidebar: #2d2d2d;

  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-muted: #888888;

  --border-color: #404040;
}

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-image: url("/img/bg-main.png") !important;
  background-size: cover !important;
  background-attachment: fixed !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
  background-color: var(--bg-main);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

#app {
  min-height: 100vh;
  background-image: url("/img/bg-main.png") !important;
  background-size: cover !important;
  background-attachment: fixed !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-track {
  background: var(--bg-main);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}


